"use client";

import React, { useState, useEffect } from "react";
import { ArrowLeft, Save } from "lucide-react";
import { fetchAiWeights, saveAiWeights } from "@/lib/api/productApi";

export default function SettingsPanel({ onClose }: { onClose: () => void }) {
  // UI states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // basic settings
  const [marketplace, setMarketplace] = useState("UK");
  const [taxSetting, setTaxSetting] = useState("US & Canadian Sellers");
  const [salesTax, setSalesTax] = useState("0.00");
  const [vatEnabled, setVatEnabled] = useState(false);
  const [roiMethod, setRoiMethod] = useState("Method 1 (Default)");
  const [prepFee, setPrepFee] = useState("0.05");
  const [bundleFee, setBundleFee] = useState("0.00");
  const [shippingCost, setShippingCost] = useState("15.00");
  const [weightUnit, setWeightUnit] = useState("kg");
  const [fulfillment, setFulfillment] = useState<"MF" | "FBA">("FBA");

  // AI Adjuster thresholds
  const [minROI, setMinROI] = useState("50.00");
  const [minProfit, setMinProfit] = useState("50.00");
  const [estSales, setEstSales] = useState("50");
  const [maxBSR, setMaxBSR] = useState("50");
  const [gatedThreshold, setGatedThreshold] = useState<boolean | null>();
  const [stockBB, setStockBB] = useState("50");
  const [amzInBB, setAmzInBB] = useState<boolean | null>();;

  // AI Adjuster weights
  const [wROI, setWROI] = useState("21");
  const [wProfit, setWProfit] = useState("13");
  const [wSales, setWSales] = useState("21");
  const [wBSR, setWBSR] = useState("3");
  const [wGated, setWGated] = useState("8");
  const [wStock, setWStock] = useState("17");
  const [wAmz, setWAmz] = useState("17");

  // Calculate total weights
  const totalWeights =
    Number(wROI) +
    Number(wProfit) +
    Number(wSales) +
    Number(wBSR) +
    Number(wGated) +
    Number(wStock) +
    Number(wAmz);

  const isValidWeights = totalWeights === 100;

  // Handle number input changes with minimum 0 validation
  const handleNumberChange = (value: string, setter: (value: string) => void) => {
    const num = Number(value);
    if (num < 0) {
      setter("0.00");
    } else {
      // Format to 2 decimal places for minROI and minProfit
      if (setter === setMinROI || setter === setMinProfit) {
        setter(Number(value).toFixed(2));
      } else {
        setter(value);
      }
    }
  };

  // load real values from API on mount
  useEffect(() => {
    setLoading(true);
    fetchAiWeights()
      .then((data) => {
        setMinROI(Math.max(0, data.minimum_roi).toFixed(2));
        setMinProfit(Math.max(0, data.minimum_profit).toFixed(2));
        setEstSales(Math.max(0, data.estimated_sales || 0).toString());
        setMaxBSR(Math.max(0, data.maximum_bsr).toString());
        setGatedThreshold(Boolean(data.gated));
        setStockBB(Math.max(0, data.stock_bb).toString());
        setAmzInBB(Boolean(data.amazon_in_bb));
        setWROI(Math.max(0, data.weight_roi).toString());
        setWProfit(Math.max(0, data.weight_profit).toString());
        setWSales(Math.max(0, data.weight_estimated_sales).toString());
        setWBSR(Math.max(0, data.weight_bsr).toString());
        setWGated(Math.max(0, data.weight_gated).toString());
        setWStock(Math.max(0, data.weight_stock_bb).toString());
        setWAmz(Math.max(0, data.weight_amazon_in_bb).toString());
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error loading settings:", err);
        setError("Unable to load settings");
        setLoading(false);
      });
  }, []);

  // Handle saving AI weights
  const handleSave = async () => {
    if (!isValidWeights) return;

    try {
      setSaving(true);
      await saveAiWeights({
        minimum_roi: parseFloat(minROI),
        minimum_profit: parseFloat(minProfit),
        estimated_sales: Math.max(0, Number(estSales)),
        maximum_bsr: Math.max(0, Number(maxBSR)),
        gated: Number(gatedThreshold) > 0,
        stock_bb: Math.max(0, Number(stockBB)),
        amazon_in_bb: Number(amzInBB),
        weight_roi: Math.max(0, Number(wROI)),
        weight_profit: Math.max(0, Number(wProfit)),
        weight_estimated_sales: Math.max(0, Number(wSales)),
        weight_bsr: Math.max(0, Number(wBSR)),
        weight_gated: Math.max(0, Number(wGated)),
        weight_stock_bb: Math.max(0, Number(wStock)),
        weight_amazon_in_bb: Math.max(0, Number(wAmz)),
      });

      setSaving(false);
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);
    } catch (err) {
      console.error("Error saving settings:", err);
      setSaving(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-100 p-2 relative">
      {/* Simple Error Message */}
      {error && (
        <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] flex items-center justify-center z-10">
          <div className="bg-white p-3 rounded-lg shadow-sm text-center">
            <div className="w-8 h-8 mx-auto mb-1 bg-blue-50 rounded-full flex items-center justify-center">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <p className="text-sm">Settings unavailable</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
            >
              Refresh
            </button>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {loading && !error && (
        <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] flex items-center justify-center z-10">
          <div className="w-8 h-8 border-2 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Header with Save Button */}
      <div className="flex items-center justify-between mb-2 px-2 py-1 bg-blue-50 rounded">
        <div className="flex items-center">
          <button
            onClick={onClose}
            title="Back"
            className="p-1 rounded hover:bg-blue-100"
          >
            <ArrowLeft size={18} className="text-blue-800" />
          </button>
          <h1 className="ml-2 text-base font-semibold text-blue-800">
            Settings
          </h1>
        </div>

        <button
          onClick={handleSave}
          disabled={saving || !isValidWeights}
          className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
            !isValidWeights
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : saving
                ? "bg-blue-400 text-white"
                : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <Save size={14} />
          <span>{saving ? "Saving..." : "Save"}</span>
        </button>
      </div>

      {/* Save Success Message */}
      {saveSuccess && (
        <div className="mb-2 px-2 py-1 bg-green-50 border border-green-100 text-green-700 text-xs rounded">
          Settings saved successfully
        </div>
      )}

      <div className="space-y-2 overflow-auto text-sm">
        {/* Fulfillment row */}
        {/* <div className="flex items-center justify-between">
          <span className="text-xs font-medium">Fulfillment</span>
          <div className="flex space-x-4 text-xs">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="fulfill"
                checked={fulfillment === 'MF'}
                onChange={() => setFulfillment('MF')}
                className="mr-1"
              />
              MF
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="fulfill"
                checked={fulfillment === 'FBA'}
                onChange={() => setFulfillment('FBA')}
                className="mr-1"
              />
              FBA
            </label>
          </div> */}
        {/* </div> */}

        {/* AI Weight Adjuster */}
        <div className="bg-white p-2 rounded shadow-sm">
          <div className="flex justify-between items-center mb-1">
            <h2 className="text-xs font-semibold text-gray-700">
              AI Weight Adjuster
            </h2>
            <div
              className={`text-xs ${isValidWeights ? "text-green-600" : "text-amber-600"}`}
            >
              Total: {totalWeights}%
            </div>
          </div>

          <div className="flex space-x-4 text-xs">
            {/* Left: Thresholds */}
            <div className="flex-1 space-y-1">
              <label>Minimum ROI</label>
              <input
                type="number"
                value={minROI}
                onChange={(e) => handleNumberChange(e.target.value, setMinROI)}
                min="0"
                step="0.01"
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Minimum Profit</label>
              <input
                type="number"
                value={minProfit}
                onChange={(e) => handleNumberChange(e.target.value, setMinProfit)}
                min="0"
                step="0.01"
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Estimated Sales</label>
              <input
                type="number"
                value={estSales}
                onChange={(e) => handleNumberChange(e.target.value, setEstSales)}
                min="0"
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Maximum BSR</label>
              <input
                type="number"
                value={maxBSR}
                onChange={(e) => handleNumberChange(e.target.value, setMaxBSR)}
                min="0"
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Gated Penalty</label>
              <select
                value={gatedThreshold !== undefined ? String(gatedThreshold) : ""}
                onChange={(e) => setGatedThreshold(e.target.value === "true")}
                className="w-full rounded-md border px-1 py-0.5"
              >
                <option value="true">True</option>
                <option value="false">False</option>
              </select>
              <label>Stock in Buy Box</label>
              <input
                type="number"
                value={stockBB}
                onChange={(e) => handleNumberChange(e.target.value, setStockBB)}
                min="0"
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Amazon-in-BB Bonus</label>
              <select
                value={amzInBB !== undefined ? String(amzInBB) : ""}
                onChange={(e) => setAmzInBB(e.target.value === "true")}
                className="w-full rounded-md border px-1 py-0.5"
              >
                <option value="true">True</option>
                <option value="false">False</option>
              </select>
            </div>

            {/* Right: Weights */}
            <div className="flex-1 space-y-1">
              <label>ROI Weight</label>
              <input
                type="number"
                value={wROI}
                onChange={(e) => handleNumberChange(e.target.value, setWROI)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Profit Weight</label>
              <input
                type="number"
                value={wProfit}
                onChange={(e) => handleNumberChange(e.target.value, setWProfit)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Sales Weight</label>
              <input
                type="number"
                value={wSales}
                onChange={(e) => handleNumberChange(e.target.value, setWSales)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>BSR Weight</label>
              <input
                type="number"
                value={wBSR}
                onChange={(e) => handleNumberChange(e.target.value, setWBSR)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Gated Weight</label>
              <input
                type="number"
                value={wGated}
                onChange={(e) => handleNumberChange(e.target.value, setWGated)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Stock BB Weight</label>
              <input
                type="number"
                value={wStock}
                onChange={(e) => handleNumberChange(e.target.value, setWStock)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Amazon-in-BB Weight</label>
              <input
                type="number"
                value={wAmz}
                onChange={(e) => handleNumberChange(e.target.value, setWAmz)}
                min="0"
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
