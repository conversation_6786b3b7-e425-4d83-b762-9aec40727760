import { useAuth } from "@/hooks/useAuth";
import { LoginCredentials, User } from "@/lib/auth/types";
import { UserCircle, X } from "lucide-react";
import { useState } from "react";
import { LoginModal } from "../auth/LoginModal";
import { ProductData } from "@/lib/types/home";

const Nav = ({
  user,
  isAuthenticated,
  error,
  logout,
  login,
  isLoading,
  setIsLoading,
  updateSectionError,
  fetchEnhancedProductData,
  setRecentLoginTimestamp,
  productInfo,
}: {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (
    credentials: LoginCredentials,
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  setRecentLoginTimestamp: (timestamp: number) => void;
  productInfo: ProductData | null;
  fetchEnhancedProductData: (asin: string) => Promise<void>;
  setIsLoading: (isLoading: boolean) => void;
  updateSectionError: (section: string, error: string | null) => void;
}) => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const handleLogin = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      setRecentLoginTimestamp(Date.now());
      const result = await login({ username, password });

      if (result.success) {
        setIsLoginModalOpen(false);

        if (productInfo && productInfo.asin) {
          if (productInfo && productInfo.asin) {
            fetchEnhancedProductData(productInfo.asin).catch((err) => {
              console.error("Error fetching data after login:", err);
              updateSectionError(
                "basic",
                "Could not refresh data after login. Please try again.",
              );
            });
          }
        }
      } else {
        //console.log("Login failed:", result.error || "Unknown error");
      }

      return result;
    } catch (err) {
      console.error("Unexpected error during login:", err);
      return {
        success: false,
        error: "An unexpected error occurred during login. Please try again.",
      };
    }
  };

  return (
    <div className="flex items-center justify-end bg-black px-2 py-1">
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onLogin={handleLogin}
        isLoading={isLoading}
        error={error}
      />
      <div className="flex items-center gap-1">
        {isAuthenticated ? (
          <div className="flex items-center">
            <div className="mr-1 flex items-center text-xs bg-gray-800 px-1 py-0.5 rounded">
              <UserCircle size={12} className="mr-1 text-white" />
              <span className="truncate max-w-[100px] text-white">
                {user?.email}
              </span>
            </div>
            <button
              onClick={logout}
              className="text-xs hover:underline text-white"
            >
              Logout
            </button>
          </div>
        ) : (
          <button
            onClick={() => setIsLoginModalOpen(true)}
            className="text-xs hover:underline mr-1 text-white"
          >
            Login
          </button>
        )}

        <button
          onClick={() => {
            //
          }}
          className="rounded p-0.5 text-xs text-white hover:bg-gray-700 transition-colors"
          title="Switch Side"
        >
          Switch
        </button>
        <button
          onClick={() => {
            //
          }}
          className="rounded-full p-0.5 transition-colors hover:bg-gray-700"
        >
          <X size={14} className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default Nav;
