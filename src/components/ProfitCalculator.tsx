"use client";

import React, { useState, useEffect, useRef } from "react";
import { fetchProfitCalculator } from "../lib/api/productApi";
import { ProductData, ProfitData } from "@/lib/types/home";

interface ProfitCalculatorProps {
  profitData: ProfitData | null;
  asin: string;
  disableInternalCalculation?: boolean;
  initialData: {
    buy_box_price: number;
    fba_fees: number;
    referral_percent: number;
    sales_rank?: number;
  };
  country?: string;
  isAuthenticated: boolean;
  onProfitCalculated?: (profitData: any) => void;
  purchasePrice?: number;
  onPurchasePriceChange?: (price: number) => void;
  buyBoxPrice?: number;
  onBuyBoxPriceChange?: (price: number) => void;
  vatPercentage?: string;
  onVatPercentageChange?: (vat: string) => void;
  isVatRegistered?: boolean;
  onVatRegisteredChange?: (isRegistered: boolean) => void;
}

// Helper function to get currency symbol based on country
const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case "US":
      return "$";
    case "UK":
    case "GB":
      return "£";
    case "DE":
    case "FR":
    case "IT":
    case "ES":
      return "€";
    case "JP":
    case "CN":
      return "¥";
    case "CA":
      return "C$";
    case "IN":
      return "₹";
    case "MX":
      return "$";
    default:
      return "£";
  }
};

const ProfitCalculator: React.FC<ProfitCalculatorProps> = ({
  profitData,
  asin,
  initialData,
  isAuthenticated,
  onProfitCalculated,
  purchasePrice,
  onPurchasePriceChange,
  buyBoxPrice,
  onBuyBoxPriceChange,
  vatPercentage: externalVatPercentage,
  onVatPercentageChange,
  isVatRegistered: externalIsVatRegistered,
  onVatRegisteredChange,
  country = "GB",
  disableInternalCalculation = false,
}) => {
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);


  // UI state
  const [isVatRegistered, setIsVatRegistered] = useState<boolean>(
    externalIsVatRegistered || false,
  );
  const [vatPercentage, setVatPercentage] = useState<string>(
    externalVatPercentage || "20",
  );
  const [internalBuyBoxPrice, setInternalBuyBoxPrice] = useState<number>(
    buyBoxPrice || initialData.buy_box_price || 0,
  );
  const [internalPurchasePrice, setInternalPurchasePrice] = useState<number>(
    purchasePrice || 0,
  );

  // Refs for debounce timeouts
  const vatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevFeeDataRef = useRef(null);
  const prevVatStatusRef = useRef(isVatRegistered);

  // Update internal state when external props change
  useEffect(() => {
    if (
      externalVatPercentage !== undefined &&
      externalVatPercentage !== vatPercentage
    ) {
      setVatPercentage(externalVatPercentage);
    }
  }, [externalVatPercentage, vatPercentage]);

  useEffect(() => {
    if (
      externalIsVatRegistered !== undefined &&
      externalIsVatRegistered !== isVatRegistered
    ) {
      setIsVatRegistered(externalIsVatRegistered);
    }
  }, [externalIsVatRegistered, isVatRegistered]);

  useEffect(() => {
    if (buyBoxPrice !== undefined && buyBoxPrice !== internalBuyBoxPrice) {
      setInternalBuyBoxPrice(buyBoxPrice);
    }
  }, [buyBoxPrice, internalBuyBoxPrice]);

  useEffect(() => {
    if (
      purchasePrice !== undefined &&
      purchasePrice !== internalPurchasePrice
    ) {
      setInternalPurchasePrice(purchasePrice);
    }
  }, [purchasePrice, internalPurchasePrice]);

  // Fetch profit data from API
  const fetchProfitData = async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const currentPurchasePrice =
        purchasePrice !== undefined ? purchasePrice : internalPurchasePrice;
      const currentBuyBoxPrice =
        buyBoxPrice !== undefined ? buyBoxPrice : internalBuyBoxPrice;

      if (buyBoxPrice === 0) {
        return;
      }

      const response = await fetchProfitCalculator({
        buy_box_price: currentBuyBoxPrice,
        fba_fees: initialData.fba_fees || 0,
        referral_percent: initialData.referral_percent || 15,
        seller_price: currentPurchasePrice,
        variable_closing_fee: 0,
        sales_rank: initialData.sales_rank || 0,
        country: country,
        vat: parseFloat(vatPercentage) || 20,
      });

      setApiResponse(response);

      if (onProfitCalculated) {
        const dataWithPrices = {
          ...response,
          purchase_price: currentPurchasePrice,
          buy_box_price: currentBuyBoxPrice,
          currentVatStatus: isVatRegistered,
          country: country,
        };

        onProfitCalculated(dataWithPrices);
      }
    } catch (err) {
      //console.log("Calculation update needed:", err);
      setError("ClickBuy is updating");
    } finally {
      setIsLoading(false);
    }
  };

  // Listen for purchase price changes
  useEffect(() => {
    if (
      isAuthenticated &&
      purchasePrice !== undefined &&
      !disableInternalCalculation
    ) {
      const timer = setTimeout(() => {
        fetchProfitData();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [purchasePrice, isAuthenticated, disableInternalCalculation]);

  // useEffect(() => {
  //   if (isAuthenticated && buyBoxPrice !== undefined) {
  //     const timer = setTimeout(() => {
  //       fetchProfitData();
  //     }, 300);

  //     return () => clearTimeout(timer);
  //   }
  // }, [buyBoxPrice, isAuthenticated]);

  // Refetch when country changes
  useEffect(() => {
    if (isAuthenticated) {
      const timer = setTimeout(() => {
        fetchProfitData();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [country, isAuthenticated]);

  // Handle VAT registration toggle
  const handleVatRegisteredChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const newIsVatRegistered = e.target.checked;

    if (newIsVatRegistered === isVatRegistered) return;

    setIsVatRegistered(newIsVatRegistered);

    if (onVatRegisteredChange) {
      onVatRegisteredChange(newIsVatRegistered);
    }

    if (apiResponse) {
      //console.log("VAT registration changed, updating UI data selection");
    } else {
      //console.log("No API response yet, triggering fetch for new VAT status");
      fetchProfitData();
    }
  };

  // Handle VAT percentage change
  const handleVatPercentageChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const newValue = e.target.value.replace(/[^0-9.]/g, "");

    setVatPercentage(newValue);

    if (onVatPercentageChange) {
      onVatPercentageChange(newValue);
    }

    if (vatTimeoutRef.current) {
      clearTimeout(vatTimeoutRef.current);
    }

    vatTimeoutRef.current = setTimeout(() => {
      fetchProfitData();
    }, 500);
  };

  // Initial data fetch
  useEffect(() => {
    if (isAuthenticated) {
      fetchProfitData();
    }

    return () => {
      if (vatTimeoutRef.current) {
        clearTimeout(vatTimeoutRef.current);
      }
    };
  }, [isAuthenticated]);

  // Format currency display with dynamic currency symbol
  const formatCurrency = (value: number | undefined | null): string => {
    if (value === undefined || value === null)
      return `${getCurrencySymbol(country)}0.00`;
    return `${getCurrencySymbol(country)}${value.toFixed(2)}`;
  };

  // Get the current fee data based on VAT registration status
  const getCurrentFeeData = () => {
    if (!apiResponse) return null;

    return isVatRegistered
      ? apiResponse.vat_registed?.fees
      : apiResponse.not_vat_registed?.fees;
  };

  // Get metrics data from API response
  const getMetricsData = () => {
    return apiResponse?.metrics || null;
  };

  const feeData = getCurrentFeeData();
  const metricsData = getMetricsData();

  return (
    <div className="bg-white">
      <div className="p-2">
        {/* Profit and ROI - separate box, extreme left, bigger text */}
        <div className="bg-gray-100 border border-gray-400 rounded p-2 mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs font-medium">Profit:</span>
            <span
              className={`text-xs font-bold ${
                (profitData?.profit || 0) > 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {isLoading ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                formatCurrency(profitData?.profit)
              )}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs font-medium">ROI:</span>
            <span
              className={`text-xs font-bold ${
                (profitData?.roi || 0) > 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {isLoading ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                `${profitData?.roi?.toFixed(1) || "0.0"}%`
              )}
            </span>
          </div>
        </div>

        {/* All breakdown including GST/VAT and Total Fees - in one separate box */}
        <div className="bg-gray-100 border border-gray-400 rounded p-2">
          {/* GST/VAT section inside the box */}
          <div className="flex items-center gap-2 mb-2 pb-2 border-b border-gray-300">
            <input
              type="checkbox"
              checked={isVatRegistered}
              onChange={handleVatRegisteredChange}
            />
            <span className="text-xs">GST/VAT Registered</span>
            <input
              type="number"
              value={vatPercentage}
              onChange={handleVatPercentageChange}
              className="w-12 text-xs border rounded px-1"
            />
            <span className="text-xs">GST/VAT %</span>
          </div>

          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Amazon Price:</span>
              <span>{formatCurrency(metricsData?.buybox_price)}</span>
            </div>
            <div className="flex justify-between">
              <span>FBA Fee:</span>
              <span className="text-red-600">
                {formatCurrency(metricsData?.fba_fees)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Referral Fee:</span>
              <span className="text-red-600">
                {formatCurrency(metricsData?.referral_fee)}
              </span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total Fees:</span>
              <span className="text-red-600">
                {formatCurrency(feeData?.total_fees)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfitCalculator;
