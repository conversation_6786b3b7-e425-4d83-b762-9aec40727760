// PriceHistorySection.jsx - with dynamic currency
import React, { useState } from "react";
import {
  Lock,
  AlertCircle,
  TrendingDown,
  TrendingUp,
  BarChart3,
  ArrowR<PERSON>,
} from "lucide-react";
import <PERSON><PERSON><PERSON><PERSON> from "./ KeepaChart";

// Helper function to get currency symbol based on country
const getCurrencySymbol = (country) => {
  switch (country) {
    case "US":
      return "$";
    case "UK":
    case "GB":
      return "£";
    case "DE":
    case "FR":
    case "IT":
    case "ES":
      return "€";
    case "JP":
    case "CN":
      return "¥";
    case "CA":
      return "C$";
    case "IN":
      return "₹";
    case "MX":
      return "$";
    default:
      return "£";
  }
};

const PriceHistorySection = ({
  isAuthenticated,
  productInfo,
  onLoginClick,
  isError = false,
  collapsed = false,
  onToggleCollapse, // Add this
  country = "GB",
}) => {
  const [activeTimeframe, setActiveTimeframe] = useState("180days");

  // Determine which data series to show initially
  const getInitialSeries = (timeframe) => {
    switch (timeframe) {
      case "30days":
        return ["new", "buy_box"];
      case "90days":
        return ["new", "buy_box", "fba"];
      case "180days":
        return ["new", "buy_box", "fba", "fbm"];
      case "365days":
        return ["new", "buy_box"];
      default:
        return ["new", "buy_box"];
    }
  };

  // Get days number from timeframe
  const getDaysFromTimeframe = (timeframe) => {
    switch (timeframe) {
      case "30days":
        return 30;
      case "90days":
        return 90;
      case "180days":
        return 180;
      case "365days":
        return 365;
      default:
        return 90;
    }
  };

  // Format currency with dynamic symbol based on country
  const formatCurrency = (value) => {
    if (value === undefined || value === null)
      return `${getCurrencySymbol(country)}0.00`;

    try {
      const numValue = Number(value);
      if (isNaN(numValue)) return `${getCurrencySymbol(country)}0.00`;

      return `${getCurrencySymbol(country)}${numValue.toFixed(2)}`;
    } catch (error) {
      console.error("Error formatting currency:", error);
      return `${getCurrencySymbol(country)}0.00`;
    }
  };

  // Calculate price trends
  const calculateTrend = (current, average) => {
    if (!current || !average) return { isUp: false, percent: 0 };

    try {
      const currentPrice = Number(current?.price || current);
      const avgPrice = Number(average?.price || average);

      if (isNaN(currentPrice) || isNaN(avgPrice) || avgPrice === 0)
        return { isUp: false, percent: 0 };

      const percentChange = ((currentPrice - avgPrice) / avgPrice) * 100;
      return {
        isUp: percentChange > 0,
        percent: Math.abs(percentChange).toFixed(1),
      };
    } catch (error) {
      return { isUp: false, percent: 0 };
    }
  };

  // Get trend data
  const currentPrice =
    productInfo?.rank_and_price_history?.current?.new?.price || 0;
  const avg30DayPrice =
    productInfo?.rank_and_price_history?.avg30?.new?.price || 0;
  const priceTrend = calculateTrend(currentPrice, avg30DayPrice);

  // Generate placeholder price history data
  const generatePlaceholderData = () => {
    // Create sample dates for the last 5 days
    const dates = [];
    for (let i = 4; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(
        date.toLocaleDateString("en-GB", {
          year: "2-digit",
          month: "short",
          day: "numeric",
        }),
      );
    }

    return dates;
  };

  const placeholderDates = generatePlaceholderData();

  return (
    <div className=" overflow-hidden bg-white shadow">
      {/* Header */}

      {!collapsed && (
        <div className="p-2">
          {isAuthenticated ? (
            isError ? (
              <div className="flex flex-col items-center justify-center py-3">
                <AlertCircle size={24} className="text-red-500 mb-1" />
                <p className="text-center text-xs text-gray-700">
                  Error loading price history data. Please try again later.
                </p>
              </div>
            ) : productInfo?.graph_data ? (
              <>
                {/* Price summary cards */}
                <div className="grid grid-cols-3 gap-1 mb-2">
                  <div className="bg-gray-50 border border-gray-400 rounded px-2 py-1 bg-gray-100">
                    <div className="text-xs text-gray-500">Current</div>
                    <div className="font-bold">
                      {formatCurrency(currentPrice)}
                    </div>
                  </div>

                  <div className="bg-gray-50 border border-gray-400 rounded px-2 py-1 bg-gray-100">
                    <div className="text-xs text-gray-500">30d Avg</div>
                    <div className="font-bold">
                      {formatCurrency(avg30DayPrice)}
                    </div>
                  </div>

                  <div className="bg-gray-50 border border-gray-400 rounded px-2 py-1 bg-gray-100 flex flex-col">
                    <div className="text-xs text-gray-500">Trend</div>
                    <div className="flex items-center">
                      {priceTrend.isUp ? (
                        <TrendingUp size={14} className="text-red-500 mr-0.5" />
                      ) : (
                        <TrendingDown
                          size={14}
                          className="text-green-500 mr-0.5"
                        />
                      )}
                      <span
                        className={`font-bold ${priceTrend.isUp ? "text-red-500" : "text-green-500"}`}
                      >
                        {priceTrend.percent}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Chart */}
                <div className="w-full">
                  <KeepaChart
                    graphData={productInfo.graph_data}
                    initialSeries={getInitialSeries(activeTimeframe)}
                    timeRange={getDaysFromTimeframe(activeTimeframe)}
                    country={country}
                  />
                </div>

                {/* Legend */}
                <div className="mt-1 flex flex-wrap gap-2">
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mr-1"></div>
                    <span className="text-xs">Amazon</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs">FBA</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-yellow-500 rounded-full mr-1"></div>
                    <span className="text-xs">FBM</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-purple-500 rounded-full mr-1"></div>
                    <span className="text-xs">Buy Box</span>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Show price summary cards with zero values */}
                <div className="grid grid-cols-3 gap-1 mb-2">
                  <div className="bg-gray-50 border rounded p-1">
                    <div className="text-xs text-gray-500">Current</div>
                    <div className="font-bold">{formatCurrency(0)}</div>
                  </div>

                  <div className="bg-gray-50 border rounded p-1">
                    <div className="text-xs text-gray-500">30d Avg</div>
                    <div className="font-bold">{formatCurrency(0)}</div>
                  </div>

                  <div className="bg-gray-50 border rounded p-1 flex flex-col">
                    <div className="text-xs text-gray-500">Trend</div>
                    <div className="flex items-center">
                      <TrendingDown
                        size={14}
                        className="text-green-500 mr-0.5"
                      />
                      <span className="font-bold text-green-500">0.0%</span>
                    </div>
                  </div>
                </div>

                {/* Price history table with zero values */}
                <div className="border rounded mb-2">
                  <div className="text-xs font-medium p-0.5 bg-gray-50 border-b flex justify-between">
                    <span>Recent Price History</span>
                    <span className="text-red-500 font-medium">
                      ClickBuy is updating
                    </span>
                  </div>
                  <div className="p-0.5">
                    {placeholderDates.map((date, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center text-xs p-0.5 bg-white border-b last:border-0"
                      >
                        <span>{date}</span>
                        <span className="font-medium">{formatCurrency(0)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Empty chart placeholder */}
                <div className="w-full h-24 bg-gray-50 border rounded mb-2 flex items-center justify-center">
                  <BarChart3 size={24} className="text-gray-300" />
                </div>

                {/* Legend */}
                <div className="mt-1 flex flex-wrap gap-2">
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mr-1"></div>
                    <span className="text-xs">Amazon</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs">FBA</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-yellow-500 rounded-full mr-1"></div>
                    <span className="text-xs">FBM</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-purple-500 rounded-full mr-1"></div>
                    <span className="text-xs">Buy Box</span>
                  </div>
                </div>
              </>
            )
          ) : (
            <div className="relative min-h-[120px] flex items-center justify-center">
              <div className="absolute inset-0 blur-sm bg-gray-50">
                <div className="h-full w-full flex items-center justify-center">
                  <div className="w-full h-24 bg-gray-200 opacity-50"></div>
                </div>
              </div>
              <div className="z-10 flex flex-col items-center p-2">
                <Lock className="h-5 w-5 text-gray-500 mb-1" />
                <p className="text-center text-xs text-gray-700">
                  Login Required to View Price History
                </p>
                <button
                  className="mt-1.5 bg-black text-white px-2.5 py-0.5 rounded text-xs hover:bg-gray-800 flex items-center"
                  onClick={onLoginClick}
                >
                  Login to View
                  <ArrowRight size={12} className="ml-1" />
                </button>
              </div>
            </div>
          )}
          {isAuthenticated && (
            <div className="border-t mt-2 border-gray-200 px-2 py-1 flex justify-between items-center text-xs text-gray-500">
              <span>
                Historical data from {getDaysFromTimeframe(activeTimeframe)}{" "}
                days
              </span>
              <span>Updated: {new Date().toLocaleDateString()}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PriceHistorySection;
