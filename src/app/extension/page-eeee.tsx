"use client";

import AISummarizer from "@/components/ai/AISummarizer";
import Header from "@/components/extension/Header";
import Nav from "@/components/extension/Nav";
import Overview from "@/components/extension/Overview";
import { useAuth } from "@/hooks/useAuth";
import { useEnhancedProductData } from "@/hooks/useEnhanceProductData";
import useLocalStorage from "@/hooks/useLocalStorage";
import {
  ProductData,
  SectionErrorState,
  SectionLoadingState,
} from "@/lib/types/home";
import { UserCircle, X } from "lucide-react";
import { useEffect, useState } from "react";

const Home = () => {
  const [isLoading, setIsLoading] = useState(false);

  const [sectionErrors, setSectionErrors] = useState<SectionErrorState>({
    basic: null,
    priceHistory: null,
    offers: null,
    calculator: null,
    gated: null,
  });
  const [productInfo, setProductInfo] = useState<ProductData | null>(null);
  const [recentLoginTimestamp, setRecentLoginTimestamp] = useState<
    number | null
  >(null);

  const [selectedCountry, setSelectedCountry] = useLocalStorage<string>(
    "selectedCountry",
    "GB",
  );
  const {
    user,
    isAuthenticated,
    isLoading: authLoading,
    error: authError,
    login,
    logout,
  } = useAuth();

  const updateSectionLoading = (section: string, isLoading: boolean) => {
    setSectionLoading((prev) => ({
      ...prev,
      [section]: isLoading,
    }));
  };

  const updateSectionError = (section: string, error: string | null) => {
    setSectionErrors((prev) => ({
      ...prev,
      [section]: error,
    }));
  };

  //console.log(
    "Main page: About to call useEnhancedProductData with initialProductInfo:",
    productInfo,
  );

  const {
    productInfo: enhancedProductInfo,
    apiData,
    isLoading: enhancedDataLoading,
    error: enhancedDataError,
    setError,
    fetchEnhancedProductData,
    refetch,
    sectionLoading,
    setSectionLoading,
  } = useEnhancedProductData({
    isAuthenticated,
    selectedCountry,
    updateSectionLoading,
    updateSectionError,
    initialProductInfo: productInfo,
  });

  //console.log(
    "Main page: useEnhancedProductData returned with productInfo:",
    enhancedProductInfo,
  );

  return (
    <main className="flex flex-col h-screen max-h-screen overflow-hidden bg-gray-100">
      <div className="flex-shrink-0">
        <Nav
          isAuthenticated={isAuthenticated}
          user={user}
          error={authError}
          logout={logout}
          login={login}
          isLoading={authLoading}
          setIsLoading={setIsLoading}
          updateSectionError={(section: string, error: string | null) => {
            setSectionErrors((prev) => ({
              ...prev,
              [section]: error,
            }));
          }}
          fetchEnhancedProductData={fetchEnhancedProductData}
          setRecentLoginTimestamp={setRecentLoginTimestamp}
          productInfo={enhancedProductInfo}
        />

        <Header />

        <Overview
          productInfo={enhancedProductInfo}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
          fetchEnhancedProductData={fetchEnhancedProductData}
          updateSectionError={updateSectionError}
          isAuthenticated={isAuthenticated}
          login={login}
          isLoading={enhancedDataLoading}
          setIsLoading={setIsLoading}
          setRecentLoginTimestamp={setRecentLoginTimestamp}
          error={enhancedDataError}
          sectionLoading={sectionLoading}
          sectionErrors={sectionErrors}
          apiData={apiData}
          updateSectionLoading={updateSectionLoading}
          recentLoginTimestamp={recentLoginTimestamp}
          setError={setError}
        />

        <AISummarizer
          estimatedSales={productInfo?.estimated_sales || 0}
          profit={0}
          roi={0}
          isGated={false}
          // profit={profitData?.profit || 0}
          // roi={profitData?.roi || 0}
          // isGated={gatedStatus?.restrictions?.length > 0}
          bsr={
            productInfo?.bsr ||
            productInfo?.pricing?.non_vat_pricing?.sales_rank ||
            "0"
          }
          stockLevel={productInfo?.offers?.sellers_offers?.[0]?.stock || "0"}
          amazonInBuyBox={productInfo?.warnings?.amz_in_buy_box || false}
          isAuthenticated={isAuthenticated}
          buyBoxHistory={apiData?.buy_box_history || {}}
          onLoginClick={() => true}
        />
      </div>
    </main>
  );
};

export default Home;
